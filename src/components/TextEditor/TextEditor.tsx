'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  Image,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
  Loader2,
  Type,
  ChevronDown,
  Code,
  Minus,
} from 'lucide-react';
import { appTheme } from '@/app/theme';
import { resizeImage, isSupportedImageFormat, type ImageResizeOptions } from '@/utils/imageUpload';

interface TextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: number;
  maxHeight?: number;
  uploadImage?: (file: File) => Promise<string>;
  imageResizeOptions?: ImageResizeOptions;
  enableImageResize?: boolean;
}

const EditorContainer = styled.div`
  position: relative;
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  background-color: ${appTheme.colors.background.main};
  overflow: hidden;
`;

const Toolbar = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: ${props => props.$active ? '#e5e7eb' : 'transparent'};
  color: ${props => props.$active ? '#374151' : '#6b7280'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const ToolbarSeparator = styled.div`
  width: 1px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 4px;
`;

const HeadingDropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const HeadingButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  min-width: 40px;
  height: 32px;
  padding: 0 8px;
  border: none;
  border-radius: ${appTheme.borderRadius.sm};
  background: ${props => props.$active ? appTheme.colors.primary : 'transparent'};
  color: ${props => props.$active ? 'white' : appTheme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  font-weight: 500;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.background.lighter};
    color: ${props => props.$active ? 'white' : appTheme.colors.text.primary};
  }

  &:active {
    transform: scale(0.95);
  }
`;

const HeadingDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  min-width: 140px;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  box-shadow: ${appTheme.shadows.md};
  display: ${props => props.$isOpen ? 'block' : 'none'};
  margin-top: 2px;
`;

const HeadingOption = styled.button<{ $active?: boolean }>`
  display: block;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: ${props => props.$active ? appTheme.colors.background.lighter : 'transparent'};
  color: ${appTheme.colors.text.primary};
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.lighter};
  }

  &:first-child {
    border-radius: ${appTheme.borderRadius.sm} ${appTheme.borderRadius.sm} 0 0;
  }

  &:last-child {
    border-radius: 0 0 ${appTheme.borderRadius.sm} ${appTheme.borderRadius.sm};
  }
`;

const EditorContent = styled.div<{ $minHeight?: number; $maxHeight?: number }>`
  min-height: ${props => props.$minHeight || 150}px;
  max-height: ${props => props.$maxHeight || 400}px;
  overflow-y: auto;
  padding: 12px;
  font-family: inherit;
  font-size: ${appTheme.typography.fontSizes.sm};
  line-height: 1.5;
  color: ${appTheme.colors.text.primary};
  outline: none;
  
  &:empty:before {
    content: attr(data-placeholder);
    color: #9ca3af;
    pointer-events: none;
  }

  p {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  ul, ol {
    margin: 8px 0;
    padding-left: 24px;
  }

  li {
    margin: 4px 0;
  }

  a {
    color: #3b82f6;
    text-decoration: underline;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }

  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }

  /* Strikethrough text styling */
  s, del {
    text-decoration: line-through;
  }

  /* Heading styles - consistent with RichTextEditor */
  h1, h2, h3, h4, h5, h6 {
    margin: 12px 0 6px 0;
    font-weight: 600;
    line-height: 1.3;
    color: ${appTheme.colors.text.primary};
  }

  h1 {
    font-size: 1.5em;
    margin: 16px 0 8px 0;
  }
  h2 {
    font-size: 1.3em;
    margin: 14px 0 7px 0;
  }
  h3 {
    font-size: 1.2em;
    margin: 12px 0 6px 0;
  }
  h4 {
    font-size: 1.1em;
    margin: 10px 0 5px 0;
  }
  h5 {
    font-size: 1.05em;
    margin: 8px 0 4px 0;
  }
  h6 {
    font-size: 1em;
    margin: 8px 0 4px 0;
    font-weight: 500;
  }

  /* Code block styling */
  pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 12px;
    margin: 8px 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    line-height: 1.4;
  }

  /* Inline code styling */
  code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 3px;
    padding: 2px 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
  }

  /* Horizontal rule styling */
  hr {
    border: none;
    border-top: 2px solid #e9ecef;
    margin: 16px 0;
    height: 0;
  }

  .text-left {
    text-align: left;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }
`;

const ImageUploadInput = styled.input`
  display: none;
`;

const LoadingOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: ${appTheme.borderRadius.md};
`;

const LoadingContent = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: white;
  border-radius: ${appTheme.borderRadius.sm};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.secondary};

  svg {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

export default function TextEditor({
  value,
  onChange,
  placeholder = 'Start typing...',
  minHeight = 150,
  maxHeight = 400,
  uploadImage,
  imageResizeOptions = { maxWidth: 600, quality: 0.85 },
  enableImageResize = true
}: TextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [currentHeading, setCurrentHeading] = useState<string>('');
  const [showHeadingDropdown, setShowHeadingDropdown] = useState(false);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  // Handle click outside to close heading dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('[data-heading-dropdown]')) {
        setShowHeadingDropdown(false);
      }
    };

    if (showHeadingDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showHeadingDropdown]);

  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  }, [onChange]);

  const execCommand = useCallback((command: string, value?: string) => {
    try {
      // Check if execCommand is available and use it
      if (typeof document.execCommand === 'function') {
        document.execCommand(command, false, value);
      } else {
        // For browsers that don't support execCommand, handle manually
        console.warn(`execCommand not supported for: ${command}`);
      }
      editorRef.current?.focus();
      handleContentChange();
    } catch (error) {
      console.warn('Error executing command:', command, error);
      editorRef.current?.focus();
    }
  }, [handleContentChange]);

  const isCommandActive = useCallback((command: string) => {
    // Check if we're in the browser environment
    if (typeof window === 'undefined') {
      return false;
    }
    
    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return false;
      
      const range = selection.getRangeAt(0);
      const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE 
        ? range.commonAncestorContainer.parentElement 
        : range.commonAncestorContainer as Element;
      
      if (!parentElement) return false;
      
      // Check for specific formatting
      switch (command) {
        case 'bold':
          return window.getComputedStyle(parentElement).fontWeight === 'bold' || 
                 window.getComputedStyle(parentElement).fontWeight === '700' ||
                 parentElement.closest('strong, b') !== null;
        case 'italic':
          return window.getComputedStyle(parentElement).fontStyle === 'italic' ||
                 parentElement.closest('em, i') !== null;
        case 'underline':
          return window.getComputedStyle(parentElement).textDecoration.includes('underline') ||
                 parentElement.closest('u') !== null;
        case 'strikeThrough':
          return window.getComputedStyle(parentElement).textDecoration.includes('line-through') ||
                 parentElement.closest('s, del, strike') !== null;
        case 'insertUnorderedList':
          return parentElement.closest('ul') !== null;
        case 'insertOrderedList':
          return parentElement.closest('ol') !== null;
        case 'justifyLeft':
          return window.getComputedStyle(parentElement).textAlign === 'left' || 
                 window.getComputedStyle(parentElement).textAlign === 'start';
        case 'justifyCenter':
          return window.getComputedStyle(parentElement).textAlign === 'center';
        case 'justifyRight':
          return window.getComputedStyle(parentElement).textAlign === 'right';
        default:
          return false;
      }
    } catch (error) {
      console.warn('Error checking command state:', error);
      return false;
    }
  }, []);

  // Check current formatting state including headings
  const updateFormattingState = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Check for headings
    const range = selection.getRangeAt(0);
    let node: Node | null = range.commonAncestorContainer;
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentNode;
    }

    let headingTag = '';
    let currentNode = node as Element;
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.tagName && /^H[1-6]$/.test(currentNode.tagName)) {
        headingTag = currentNode.tagName;
        break;
      }
      currentNode = currentNode.parentElement as Element;
    }
    setCurrentHeading(headingTag);
  }, []);

  // Apply heading formatting
  const applyHeading = useCallback((headingLevel: string) => {
    if (!editorRef.current) return;

    editorRef.current.focus();

    if (currentHeading === headingLevel) {
      // Remove heading formatting by converting to div
      document.execCommand('formatBlock', false, 'div');
    } else {
      // Apply heading formatting
      document.execCommand('formatBlock', false, headingLevel.toLowerCase());
    }

    setShowHeadingDropdown(false);
    updateFormattingState();
    handleContentChange();
  }, [currentHeading, updateFormattingState, handleContentChange]);

  // Insert horizontal rule
  const insertHorizontalRule = useCallback(() => {
    if (!editorRef.current) return;

    editorRef.current.focus();
    document.execCommand('insertHTML', false, '<hr>');
    handleContentChange();
  }, [handleContentChange]);

  // Insert code block
  const insertCodeBlock = useCallback(() => {
    if (!editorRef.current) return;

    editorRef.current.focus();
    const selection = window.getSelection();
    const selectedText = selection?.toString() || 'Your code here';
    document.execCommand('insertHTML', false, `<pre><code>${selectedText}</code></pre>`);
    handleContentChange();
  }, [handleContentChange]);

  const handleImageUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      try {
        setIsProcessingImage(true);

        // Check if it's a supported format for resizing
        if (!isSupportedImageFormat(file)) {
          throw new Error('Unsupported image format. Please use JPEG, PNG, WebP, or GIF.');
        }

        let processedFile = file;

        // Resize image if enabled and options are provided
        if (enableImageResize && imageResizeOptions) {
          try {
            processedFile = await resizeImage(file, imageResizeOptions);
          } catch (resizeError) {
            console.warn('Failed to resize image, using original:', resizeError);
            // Continue with original file if resize fails
          }
        }

        if (uploadImage) {
          // Upload to server and get URL
          const imageUrl = await uploadImage(processedFile);
          execCommand('insertImage', imageUrl);
        } else {
          // Fallback to base64
          const reader = new FileReader();
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            execCommand('insertImage', imageUrl);
          };
          reader.readAsDataURL(processedFile);
        }
      } catch (error) {
        console.error('Failed to process/upload image:', error);
        // Fallback to base64 with original file on error
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          execCommand('insertImage', imageUrl);
        };
        reader.readAsDataURL(file);
      } finally {
        setIsProcessingImage(false);
      }
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [execCommand, uploadImage, enableImageResize, imageResizeOptions]);

  const toolbarButtons = [
    { command: 'bold', icon: Bold, title: 'Bold' },
    { command: 'italic', icon: Italic, title: 'Italic' },
    { command: 'underline', icon: Underline, title: 'Underline' },
    { command: 'strikeThrough', icon: Strikethrough, title: 'Strikethrough' },
    { separator: true },
    { command: 'insertUnorderedList', icon: List, title: 'Bullet List' },
    { command: 'insertOrderedList', icon: ListOrdered, title: 'Numbered List' },
    { separator: true },
    { command: 'justifyLeft', icon: AlignLeft, title: 'Align Left' },
    { command: 'justifyCenter', icon: AlignCenter, title: 'Align Center' },
    { command: 'justifyRight', icon: AlignRight, title: 'Align Right' },
    { separator: true },
    { command: 'undo', icon: Undo, title: 'Undo' },
    { command: 'redo', icon: Redo, title: 'Redo' },
  ] as const;

  return (
    <EditorContainer>
      <Toolbar>
        {/* Heading Dropdown */}
        <HeadingDropdownContainer data-heading-dropdown>
          <HeadingButton
            type="button"
            $active={!!currentHeading}
            onClick={() => setShowHeadingDropdown(!showHeadingDropdown)}
            title="Headings"
          >
            <Type size={16} />
            <ChevronDown size={12} />
          </HeadingButton>
          <HeadingDropdown $isOpen={showHeadingDropdown}>
            <HeadingOption
              $active={currentHeading === ''}
              onClick={() => applyHeading('')}
            >
              Normal Text
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H1'}
              onClick={() => applyHeading('H1')}
            >
              Heading 1
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H2'}
              onClick={() => applyHeading('H2')}
            >
              Heading 2
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H3'}
              onClick={() => applyHeading('H3')}
            >
              Heading 3
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H4'}
              onClick={() => applyHeading('H4')}
            >
              Heading 4
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H5'}
              onClick={() => applyHeading('H5')}
            >
              Heading 5
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H6'}
              onClick={() => applyHeading('H6')}
            >
              Heading 6
            </HeadingOption>
          </HeadingDropdown>
        </HeadingDropdownContainer>

        <ToolbarSeparator />

        {toolbarButtons.map((button, index) => {
          if ('separator' in button && button.separator) {
            return <ToolbarSeparator key={index} />;
          }

          if ('icon' in button && button.icon) {
            const Icon = button.icon;
            return (
              <ToolbarButton
                key={button.command}
                type="button"
                title={button.title}
                $active={isCommandActive(button.command)}
                onClick={() => execCommand(button.command)}
              >
                <Icon />
              </ToolbarButton>
            );
          }

          return null;
        })}

        <ToolbarSeparator />

        {/* Code Block Button */}
        <ToolbarButton
          type="button"
          title="Code Block"
          onClick={insertCodeBlock}
        >
          <Code />
        </ToolbarButton>

        {/* Horizontal Rule Button */}
        <ToolbarButton
          type="button"
          title="Horizontal Rule"
          onClick={insertHorizontalRule}
        >
          <Minus />
        </ToolbarButton>

        <ToolbarSeparator />

        <ToolbarButton
          type="button"
          title="Insert Image"
          onClick={() => fileInputRef.current?.click()}
          disabled={isProcessingImage}
        >
          <Image />
        </ToolbarButton>
      </Toolbar>

      <EditorContent
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning
        data-placeholder={placeholder}
        $minHeight={minHeight}
        $maxHeight={maxHeight}
        onInput={handleContentChange}
        onPaste={handleContentChange}
        onClick={updateFormattingState}
        onKeyUp={updateFormattingState}
        onMouseUp={updateFormattingState}
      />

      <ImageUploadInput
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        disabled={isProcessingImage}
      />

      {isProcessingImage && (
        <LoadingOverlay>
          <LoadingContent>
            <Loader2 size={16} />
            Resizing image...
          </LoadingContent>
        </LoadingOverlay>
      )}
    </EditorContainer>
  );
}
