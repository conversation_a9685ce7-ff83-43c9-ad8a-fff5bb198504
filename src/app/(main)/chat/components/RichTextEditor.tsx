'use client';

import React, { useRef, useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { Bold, List, Underline, Type, ChevronDown } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { MentionUser, replaceMentionsWithHTML, extractTextFromHTML } from '@/types/mention';
import { useMentions } from '@/hooks/useMentions';
import MentionPopover from '@/components/MentionPopover/MentionPopover';

interface RichTextEditorProps {
  placeholder?: string;
  value?: string;
  onChange?: (htmlContent: string, textContent: string) => void;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  disabled?: boolean;
  maxHeight?: string;
  minHeight?: string;
  // Mention functionality props
  mentionUsers?: MentionUser[];
  enableMentions?: boolean;
}

const EditorContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  border: none;
  background: transparent;
`;

const ToolbarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};
  border-bottom: 1px solid ${appTheme.colors.border};
  margin-bottom: ${appTheme.spacing.xs};
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 25px;
  border: none;
  border-radius: ${appTheme.borderRadius.sm};
  background: transparent;
  color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
    color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.primary};
  }

  &:active {
    transform: scale(0.95);
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    width: 32px;
    height: 32px;
  }
`;

const HeadingDropdownContainer = styled.div`
  position: relative;
  display: inline-block;
`;

const HeadingButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  min-width: 40px;
  height: 25px;
  padding: 0 4px;
  border: none;
  border-radius: ${appTheme.borderRadius.sm};
  background: transparent;
  color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.secondary};
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  font-weight: 500;

  &:hover {
    background: ${props => props.$active ? appTheme.colors.primaryLight : appTheme.colors.background.lighter};
    color: ${props => props.$active ? appTheme.colors.primary : appTheme.colors.text.primary};
  }

  &:active {
    transform: scale(0.95);
  }

  /* Mobile - touch-friendly sizing */
  @media (max-width: ${appTheme.breakpoints.md}) {
    min-width: 44px;
    height: 32px;
    font-size: 12px;
  }
`;

const HeadingDropdown = styled.div<{ $isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  min-width: 120px;
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.sm};
  box-shadow: ${appTheme.shadows.md};
  display: ${props => props.$isOpen ? 'block' : 'none'};
  margin-top: 2px;
`;

const HeadingOption = styled.button<{ $active?: boolean }>`
  display: block;
  width: 100%;
  padding: 6px 12px;
  border: none;
  background: ${props => props.$active ? appTheme.colors.background.lighter : 'transparent'};
  color: ${appTheme.colors.text.primary};
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.lighter};
  }

  &:first-child {
    border-radius: ${appTheme.borderRadius.sm} ${appTheme.borderRadius.sm} 0 0;
  }

  &:last-child {
    border-radius: 0 0 ${appTheme.borderRadius.sm} ${appTheme.borderRadius.sm};
  }
`;

const EditorContent = styled.div<{ $maxHeight?: string; $minHeight?: string }>`
  flex: 1;
  outline: none;
  font-size: 14px;
  font-family: inherit;
  line-height: 1.4;
  color: ${appTheme.colors.text.primary};
  max-height: ${props => props.$maxHeight || '120px'};
  min-height: ${props => props.$minHeight || '20px'};
  overflow-y: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;

  &:empty::before {
    content: attr(data-placeholder);
    color: ${appTheme.colors.text.light};
    pointer-events: none;
  }

  /* Style for bold text */
  strong, b {
    font-weight: 600;
  }

  /* Style for underline text */
  u {
    text-decoration: underline;
  }

  /* Style for headings */
  h1, h2, h3, h4, h5, h6 {
    margin: 8px 0 4px 0;
    font-weight: 600;
    line-height: 1.3;
  }

  h1 { font-size: 1.5em; }
  h2 { font-size: 1.3em; }
  h3 { font-size: 1.2em; }
  h4 { font-size: 1.1em; }
  h5 { font-size: 1.05em; }
  h6 { font-size: 1em; }

  /* Style for bullet lists */
  ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: disc;
  }

  li {
    margin: 2px 0;
  }

  /* Prevent nested lists for simplicity */
  ul ul {
    display: none;
  }

  /* Style for mentions */
  .mention[data-mention="true"] {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    user-select: none; /* Prevent text selection within mention */

    &:hover {
      background-color: #bbdefb;
    }
  }

  /* Mobile - larger font and better touch experience */
  @media (max-width: ${appTheme.breakpoints.md}) {
    font-size: 16px; /* Prevents zoom on iOS */
    line-height: 1.5;
    min-height: 24px;
    max-height: 100px;
  }

  /* Small mobile - optimize for smaller screens */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    max-height: 80px;
  }

  /* Enhanced scrollbar styling */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: ${appTheme.colors.border};
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: ${appTheme.colors.text.light};
  }
`;

export default function RichTextEditor({
  placeholder = 'Type your message...',
  value = '',
  onChange,
  onKeyDown,
  disabled = false,
  maxHeight,
  minHeight,
  mentionUsers = [],
  enableMentions = true,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [isBoldActive, setIsBoldActive] = useState(false);
  const [isListActive, setIsListActive] = useState(false);
  const [isUnderlineActive, setIsUnderlineActive] = useState(false);
  const [currentHeading, setCurrentHeading] = useState<string>(''); // 'H1', 'H2', etc., or '' for none
  const [showHeadingDropdown, setShowHeadingDropdown] = useState(false);

  // Initialize mention functionality
  const mentionHook = useMentions({
    users: mentionUsers,
    editorRef,
    onMentionInsert: () => {
      // Trigger content change when mention is inserted
      handleInput();
    }
  }) as any; // Type assertion to handle the extended interface

  // Update editor content when value prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.innerHTML !== value) {
      // Convert mention format to HTML if mentions are enabled
      const htmlContent = enableMentions ? replaceMentionsWithHTML(value) : value;
      editorRef.current.innerHTML = htmlContent;
    }
  }, [value, enableMentions]);

  // Check current formatting state
  const updateFormattingState = useCallback(() => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Check if bold is active
    const boldActive = document.queryCommandState('bold');
    setIsBoldActive(boldActive);

    // Check if underline is active
    const underlineActive = document.queryCommandState('underline');
    setIsUnderlineActive(underlineActive);

    // Check if we're in a list
    const range = selection.getRangeAt(0);
    let node: Node | null = range.commonAncestorContainer;
    if (node.nodeType === Node.TEXT_NODE) {
      node = node.parentNode;
    }

    let listActive = false;
    let headingTag = '';
    let currentNode = node as Element;
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.tagName === 'UL' || currentNode.tagName === 'LI') {
        listActive = true;
      }
      if (currentNode.tagName && /^H[1-6]$/.test(currentNode.tagName)) {
        headingTag = currentNode.tagName;
      }
      currentNode = currentNode.parentElement as Element;
    }
    setIsListActive(listActive);
    setCurrentHeading(headingTag);
  }, []);

  // Handle content changes
  const handleInput = useCallback(() => {
    if (!editorRef.current || !onChange) return;

    const htmlContent = editorRef.current.innerHTML;
    let textContent = editorRef.current.textContent || '';

    // Convert mentions back to storage format if mentions are enabled
    if (enableMentions) {
      textContent = extractTextFromHTML(htmlContent);
    }

    onChange(htmlContent, textContent);
    updateFormattingState();

    // Check for mention triggers if mentions are enabled
    if (enableMentions && mentionHook.checkForMentionTrigger) {
      setTimeout(() => mentionHook.checkForMentionTrigger(), 0);
    }
  }, [onChange, updateFormattingState, enableMentions, mentionHook]);

  // Handle selection changes
  const handleSelectionChange = useCallback(() => {
    updateFormattingState();
  }, [updateFormattingState]);

  // Handle key events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    // Handle mention navigation first if mentions are enabled and popover is visible
    if (enableMentions && mentionHook.handleMentionKeyDown && mentionHook.handleMentionKeyDown(e)) {
      return; // Mention navigation handled the key event
    }

    // Handle Enter key for list items - check this FIRST before calling onKeyDown
    if (e.key === 'Enter' && isListActive && !e.shiftKey) {
      e.preventDefault();
      e.stopPropagation(); // ป้องกันไม่ให้ event ไปถึง parent component

      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;

      const range = selection.getRangeAt(0);
      let listItem: Node | null = range.commonAncestorContainer;

      // Find the current list item
      while (listItem && (listItem.nodeType !== Node.ELEMENT_NODE || (listItem as Element).tagName !== 'LI')) {
        listItem = listItem.parentNode;
      }

      if (listItem && (listItem as Element).tagName === 'LI') {
        const currentLi = listItem as HTMLLIElement;
        
        // If the current list item is empty, exit the list
        if (!currentLi.textContent?.trim()) {
          const ul = currentLi.parentElement;
          if (ul && ul.tagName === 'UL') {
            // Remove the empty list item
            currentLi.remove();
            
            // Create a new paragraph after the list
            const newP = document.createElement('div');
            newP.innerHTML = '<br>';
            ul.parentNode?.insertBefore(newP, ul.nextSibling);
            
            // Move cursor to the new paragraph
            const newRange = document.createRange();
            newRange.setStart(newP, 0);
            newRange.collapse(true);
            selection.removeAllRanges();
            selection.addRange(newRange);
          }
        } else {
          // Create a new list item
          const newLi = document.createElement('li');
          newLi.innerHTML = '<br>';
          currentLi.parentNode?.insertBefore(newLi, currentLi.nextSibling);
          
          // Move cursor to the new list item
          const newRange = document.createRange();
          newRange.setStart(newLi, 0);
          newRange.collapse(true);
          selection.removeAllRanges();
          selection.addRange(newRange);
        }
        
        handleInput();
        return; // ออกจาก function เลยไม่ต้องเรียก onKeyDown
      }
    }

    // เรียก onKeyDown สำหรับ key อื่นๆ หรือเมื่อไม่ใช่ Enter ใน bullet list
    if (onKeyDown) {
      onKeyDown(e);
    }
  }, [onKeyDown, isListActive, handleInput, enableMentions, mentionHook]);

  // Format text as bold
  const toggleBold = useCallback(() => {
    if (!editorRef.current) return;

    editorRef.current.focus();
    document.execCommand('bold', false);
    updateFormattingState();
    handleInput();
  }, [updateFormattingState, handleInput]);

  // Format text as underline
  const toggleUnderline = useCallback(() => {
    if (!editorRef.current) return;

    editorRef.current.focus();
    document.execCommand('underline', false);
    updateFormattingState();
    handleInput();
  }, [updateFormattingState, handleInput]);

  // Apply heading formatting
  const applyHeading = useCallback((headingLevel: string) => {
    if (!editorRef.current) return;

    editorRef.current.focus();

    if (currentHeading === headingLevel) {
      // Remove heading formatting by converting to div
      document.execCommand('formatBlock', false, 'div');
    } else {
      // Apply heading formatting
      document.execCommand('formatBlock', false, headingLevel.toLowerCase());
    }

    setShowHeadingDropdown(false);
    updateFormattingState();
    handleInput();
  }, [currentHeading, updateFormattingState, handleInput]);

  // Toggle bullet list
  const toggleList = useCallback(() => {
    if (!editorRef.current) return;
    
    editorRef.current.focus();
    
    if (isListActive) {
      // Remove list formatting
      document.execCommand('insertHTML', false, '<div><br></div>');
    } else {
      // Add list formatting
      document.execCommand('insertUnorderedList', false);
    }
    
    updateFormattingState();
    handleInput();
  }, [isListActive, updateFormattingState, handleInput]);

  // Handle click outside to close dropdown
  const handleClickOutside = useCallback((event: MouseEvent) => {
    const target = event.target as Element;
    if (!target.closest('[data-heading-dropdown]')) {
      setShowHeadingDropdown(false);
    }
  }, []);

  // Set up event listeners
  useEffect(() => {
    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [handleSelectionChange, handleClickOutside]);

  return (
    <EditorContainer>
      <ToolbarContainer>
        <ToolbarButton
          type="button"
          $active={isBoldActive}
          onClick={toggleBold}
          title="Bold (Ctrl+B)"
          disabled={disabled}
        >
          <Bold size={18} />
        </ToolbarButton>
        <ToolbarButton
          type="button"
          $active={isUnderlineActive}
          onClick={toggleUnderline}
          title="Underline (Ctrl+U)"
          disabled={disabled}
        >
          <Underline size={18} />
        </ToolbarButton>
        <HeadingDropdownContainer data-heading-dropdown>
          <HeadingButton
            type="button"
            $active={!!currentHeading}
            onClick={() => setShowHeadingDropdown(!showHeadingDropdown)}
            title="Heading"
            disabled={disabled}
          >
            <Type size={14} />
            {currentHeading || 'H'}
            <ChevronDown size={12} />
          </HeadingButton>
          <HeadingDropdown $isOpen={showHeadingDropdown}>
            <HeadingOption
              $active={!currentHeading}
              onClick={() => applyHeading('')}
            >
              Normal
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H1'}
              onClick={() => applyHeading('H1')}
            >
              Heading 1
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H2'}
              onClick={() => applyHeading('H2')}
            >
              Heading 2
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H3'}
              onClick={() => applyHeading('H3')}
            >
              Heading 3
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H4'}
              onClick={() => applyHeading('H4')}
            >
              Heading 4
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H5'}
              onClick={() => applyHeading('H5')}
            >
              Heading 5
            </HeadingOption>
            <HeadingOption
              $active={currentHeading === 'H6'}
              onClick={() => applyHeading('H6')}
            >
              Heading 6
            </HeadingOption>
          </HeadingDropdown>
        </HeadingDropdownContainer>
        <ToolbarButton
          type="button"
          $active={isListActive}
          onClick={toggleList}
          title="Bullet List"
          disabled={disabled}
        >
          <List size={18} />
        </ToolbarButton>
      </ToolbarContainer>
      
      <EditorContent
        ref={editorRef}
        contentEditable={!disabled}
        data-placeholder={placeholder}
        onInput={handleInput}
        onKeyDown={handleKeyDown}
        $maxHeight={maxHeight}
        $minHeight={minHeight}
        suppressContentEditableWarning={true}
      />

      {/* Mention Popover */}
      {enableMentions && (
        <MentionPopover
          isVisible={mentionHook.showMentionPopover}
          position={mentionHook.mentionPosition}
          users={mentionHook.filteredUsers}
          query={mentionHook.mentionQuery}
          selectedIndex={mentionHook.selectedMentionIndex}
          onUserSelect={mentionHook.handleMentionSelect}
          onClose={mentionHook.closeMentionPopover}
        />
      )}
    </EditorContainer>
  );
}
